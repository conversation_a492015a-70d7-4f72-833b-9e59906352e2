# Abstract and Keywords for High-Impact Journal

## Title

**Linguistic Pattern Analysis for Fatigue Risk Prediction in University Students: A Machine Learning Approach Using Digital Activity Data**

## Abstract

**Background**: Fatigue among university students significantly impacts academic performance and well-being, yet current assessment methods rely on subjective self-reporting or expensive physiological monitoring. Digital activity platforms generate rich behavioral data, but the potential of linguistic patterns in user-generated content for health monitoring remains largely unexplored.

**Objective**: To develop and validate a machine learning framework for predicting fatigue risk in university students using linguistic pattern analysis of digital activity descriptions, combined with cardiovascular and productivity metrics.

**Methods**: We collected data from 106 Indonesian university students over 291 weekly observations, integrating Strava cardiovascular tracking and Pomokit productivity management platforms. We developed a comprehensive feature engineering framework extracting 20 features across linguistic, temporal, behavioral, and gamification dimensions. Four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) were evaluated using a dual strategy combining train-test split for interpretability analysis and stratified k-fold cross-validation for robustness assessment. SHAP (SHapley Additive exPlanations) analysis provided interpretable feature importance rankings with cross-algorithm validation. Systematic ablation studies validated feature contributions, and bias correction methodology addressed data leakage concerns.

**Results**: Linguistic features dominated predictive importance, with `pomokit_unique_words` (5.51%), `total_title_diversity` (5.33%), and `title_balance_ratio` (5.19%) consistently ranking as top predictors across all algorithms (100% consensus, variance <0.05). XGBoost achieved highest test accuracy (79.66%, F1-score: 0.795) but showed significant overfitting (train-validation gap: 12.90%, overfitting score: 22.01). Logistic Regression demonstrated superior stability (test accuracy: 71.19%, train-validation gap: 1.84%, overfitting score: 9.23). SHAP-based feature selection outperformed random selection by up to 1.91% for Gradient Boosting. Title-only analysis achieved competitive accuracy without comprehensive quantitative data. Linguistic diversity showed progressive increase across risk categories (low: 3.2±1.1, medium: 4.8±1.4, high: 6.3±1.8 unique words, p<0.001).

**Conclusions**: This study demonstrates that linguistic patterns in digital activity descriptions serve as powerful predictors of fatigue risk, outperforming traditional quantitative metrics. The dominance of linguistic features suggests that language complexity reflects underlying cognitive load and psychological states associated with fatigue. Our methodology enables scalable, privacy-preserving fatigue monitoring through analysis of routine digital interactions. The trade-off between model performance and stability highlights the importance of choosing appropriate algorithms for health applications, with stable models preferred for clinical deployment despite lower peak accuracy. These findings provide a foundation for developing next-generation digital health monitoring systems that leverage natural language processing for early detection and intervention in student fatigue.

**Clinical Relevance**: The linguistic-based approach offers practical advantages including non-intrusive monitoring, real-time analysis capability, privacy preservation, and integration with existing digital platforms. This methodology can inform adaptive learning systems, early warning mechanisms for student support services, and personalized intervention strategies based on individual linguistic patterns.

## Keywords

**Primary Keywords**:
- Digital health monitoring
- Fatigue prediction
- Natural language processing
- Machine learning
- University students
- Linguistic analysis

**Secondary Keywords**:
- SHAP analysis
- Feature importance
- Digital phenotyping
- Behavioral analytics
- Educational technology
- Student well-being

**Technical Keywords**:
- Cross-validation
- Overfitting detection
- Bias correction
- Interpretable machine learning
- Multi-modal data integration
- Text mining

**Application Keywords**:
- Student health
- Academic performance
- Productivity monitoring
- Cardiovascular tracking
- Gamification
- Early intervention

## Subject Categories

**Primary Categories**:
- Computer Science, Interdisciplinary Applications
- Medical Informatics
- Engineering, Biomedical
- Psychology, Applied
- Education & Educational Research

**Secondary Categories**:
- Computer Science, Artificial Intelligence
- Public, Environmental & Occupational Health
- Behavioral Sciences
- Linguistics
- Human Factors & Ergonomics

## Journal Target Recommendations

### Tier 1 Journals (Impact Factor >8)

1. **Nature Digital Medicine** (IF: 12.3)
   - Focus: Digital health innovation and validation
   - Relevance: Novel digital biomarkers and health monitoring
   - Strengths: Methodological innovation, clinical relevance

2. **npj Digital Medicine** (IF: 9.8)
   - Focus: Digital health technologies and applications
   - Relevance: Machine learning in health, digital phenotyping
   - Strengths: Open access, interdisciplinary approach

3. **Journal of Medical Internet Research** (IF: 8.4)
   - Focus: Digital health, eHealth, mHealth applications
   - Relevance: Student health monitoring, digital interventions
   - Strengths: Comprehensive methodology, practical applications

### Tier 2 Journals (Impact Factor 5-8)

4. **Computers in Human Behavior** (IF: 7.9)
   - Focus: Human-computer interaction, digital behavior
   - Relevance: Digital behavior analysis, student technology use
   - Strengths: Behavioral insights, educational applications

5. **IEEE Journal of Biomedical and Health Informatics** (IF: 7.7)
   - Focus: Biomedical informatics, health technology
   - Relevance: Machine learning in health, signal processing
   - Strengths: Technical rigor, algorithmic innovation

6. **Artificial Intelligence in Medicine** (IF: 7.5)
   - Focus: AI applications in healthcare
   - Relevance: Machine learning for health prediction
   - Strengths: AI methodology, clinical applications

### Tier 3 Journals (Impact Factor 3-5)

7. **Computers & Education** (IF: 4.8)
   - Focus: Educational technology, learning analytics
   - Relevance: Student monitoring, educational applications
   - Strengths: Educational context, practical implications

8. **Behavior Research Methods** (IF: 4.6)
   - Focus: Research methodology, behavioral measurement
   - Relevance: Digital behavior measurement, validation methods
   - Strengths: Methodological contributions, measurement innovation

9. **International Journal of Human-Computer Studies** (IF: 4.4)
   - Focus: HCI, user behavior, interface design
   - Relevance: Digital platform analysis, user interaction patterns
   - Strengths: User-centered approach, interaction analysis

## Manuscript Preparation Guidelines

### Abstract Structure (250-300 words)
- **Background** (50-60 words): Problem statement and motivation
- **Objective** (30-40 words): Clear research aims and questions
- **Methods** (80-100 words): Study design, participants, methodology
- **Results** (80-100 words): Key findings with specific metrics
- **Conclusions** (40-50 words): Implications and significance

### Key Messaging Strategy

**Primary Message**: Linguistic patterns in digital activity descriptions are more predictive of fatigue than traditional quantitative metrics, enabling scalable, privacy-preserving health monitoring.

**Supporting Messages**:
1. Cross-algorithm consensus validates robustness of linguistic feature importance
2. Model stability considerations are crucial for health application deployment
3. Title-only analysis enables privacy-preserving monitoring approaches
4. Methodology provides framework for next-generation digital health systems

### Novelty and Impact Statements

**Methodological Novelty**:
- First comprehensive linguistic analysis for fatigue prediction
- Novel SHAP-based cross-algorithm feature validation
- Innovative bias correction framework for digital health data
- Dual evaluation strategy addressing stability-performance trade-offs

**Scientific Impact**:
- Paradigm shift from quantitative to linguistic health monitoring
- Evidence for language as digital biomarker of psychological states
- Framework for interpretable machine learning in health applications
- Foundation for privacy-preserving population health surveillance

**Practical Impact**:
- Scalable monitoring without additional hardware requirements
- Integration pathway for existing digital platforms
- Early warning system for educational institutions
- Personalized intervention based on individual linguistic patterns

### Statistical Reporting Standards

**Model Performance Metrics**:
- Accuracy, F1-score (macro), Precision (macro), Recall (macro)
- 95% confidence intervals for all performance metrics
- Cross-validation results with mean ± standard deviation
- Statistical significance testing for model comparisons

**Feature Importance Reporting**:
- SHAP values with confidence intervals
- Cross-algorithm consensus scores
- Ablation study results with effect sizes
- Statistical significance of feature contributions

**Reproducibility Information**:
- Complete hyperparameter specifications
- Random seed documentation (seed=42)
- Library versions and environment specifications
- Code and data availability statements

This comprehensive abstract and keyword framework positions the manuscript for high-impact journal submission while ensuring compliance with reporting standards and maximizing discoverability through appropriate keyword selection.

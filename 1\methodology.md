# Methodology for High-Impact Journal Publication

## Title: "Linguistic Pattern Analysis for Fatigue Risk Prediction in University Students: A Machine Learning Approach Using Digital Activity Data"

## Abstract

This study presents a novel methodology for predicting fatigue risk in university students through linguistic pattern analysis of digital activity descriptions. Using data from 106 students with 291 weekly observations, we developed a machine learning framework that leverages natural language processing of activity titles combined with cardiovascular and productivity metrics. Our SHAP-based feature importance analysis revealed that linguistic diversity features (pomokit_unique_words: 5.54%, total_title_diversity: 5.33%, title_balance_ratio: 5.19%) significantly outperformed traditional quantitative metrics in predicting fatigue risk. The XGBoost classifier achieved 79.66% test accuracy with F1-score of 79.54%, while Logistic Regression demonstrated superior stability with minimal overfitting (score: 9.23). This methodology introduces a paradigm shift from purely quantitative to linguistically-informed fatigue prediction, offering practical implications for digital health monitoring systems.

## 1. Introduction and Research Framework

### 1.1 Problem Statement

University student fatigue represents a critical public health concern affecting academic performance, mental well-being, and long-term career outcomes. Traditional fatigue assessment methods rely heavily on subjective self-reporting or expensive physiological monitoring, limiting their scalability and real-time applicability. This study addresses the gap by developing an objective, scalable methodology for fatigue risk prediction using readily available digital activity data.

### 1.2 Research Questions

1. **RQ1**: Which linguistic and behavioral features extracted from digital activity descriptions are most predictive of fatigue risk in university students?
2. **RQ2**: How do machine learning models perform in classifying fatigue risk levels using multi-modal digital data?
3. **RQ3**: Can title-only analysis provide accurate fatigue prediction without requiring comprehensive quantitative data?

### 1.3 Methodological Innovation

Our methodology introduces three key innovations:

-   **Linguistic Feature Engineering**: Novel extraction of semantic and syntactic features from activity descriptions
-   **SHAP-based Feature Validation**: Systematic ablation study for feature importance verification
-   **Bias-Corrected Classification**: Framework to mitigate data leakage and improve generalizability

## 2. Data Collection and Preprocessing

### 2.1 Study Population and Sampling

**Participants**: 106 Indonesian university students
**Raw Data Points**: 722 Strava activities + 1,240 Pomokit sessions
**Aggregated Observations**: 291 weekly data points
**Study Design**: Cross-sectional with longitudinal elements
**Data Collection Period**: March 2025 (ongoing data collection)
**Minimum Observation**: 4-week continuous data per participant

**Inclusion Criteria**:

-   Active university enrollment in Indonesian institutions
-   Consistent use of Strava platform for cardiovascular activity tracking
-   Active use of Pomokit platform for productivity management
-   Minimum 4 weeks of continuous data across both platforms
-   Adequate digital literacy for mobile app usage
-   Valid phone number for participant identification

**Exclusion Criteria**:

-   Incomplete activity data (<4 weeks continuous)
-   Inconsistent data patterns indicating non-genuine usage
-   Medical conditions significantly affecting physical activity capacity
-   Evidence of data manipulation or automated entries
-   Missing critical demographic information

### 2.2 Data Sources and Integration

**Cardiovascular Data (Strava Platform)**:
Raw data structure from Strava CSV includes:

-   **Activity Metrics**: `distance` (km), `moving_time` (duration), `elevation` (m)
-   **Descriptive Data**: `title` (activity descriptions), `name` (user names)
-   **Temporal Data**: `date_time` (activity timestamps), `type_sport` (activity type)
-   **User Identification**: `athlete_id`, `phone_number` for cross-platform matching
-   **Platform Data**: `activity_id`, `link_activity`, `status` (validation)

**Productivity Data (Pomokit Platform)**:
Raw data structure from Pomokit CSV includes:

-   **Session Metrics**: `cycle` (Pomodoro cycles completed), `screenshots` (work evidence)
-   **Task Descriptions**: `pekerjaan` (task descriptions), `urlpekerjaan` (work URLs)
-   **Technical Data**: `hostname`, `ip` (device identification), `token` (session validation)
-   **User Identification**: `phonenumber` for cross-platform matching with Strava
-   **Temporal Data**: `createdAt` (session timestamps)
-   **Group Data**: `wagroupid` (WhatsApp group identification for class cohorts)

### 2.3 Data Preprocessing Pipeline

```python
def comprehensive_preprocessing_pipeline(strava_data, pomokit_data):
    """
    Comprehensive data preprocessing with bias correction
    """
    # Phase 1: Data Cleaning
    strava_clean = clean_strava_data(strava_data)
    pomokit_clean = clean_pomokit_data(pomokit_data)

    # Phase 2: Weekly Aggregation
    weekly_data = create_weekly_aggregation(strava_clean, pomokit_clean)

    # Phase 3: Feature Engineering
    engineered_data = engineer_linguistic_features(weekly_data)

    # Phase 4: Bias Correction
    corrected_data = apply_bias_correction(engineered_data)

    return corrected_data
```

**Key Preprocessing Steps**:

1. **Standardization**: Column naming, date format conversion
2. **Data Cleaning**: Outlier removal, missing value imputation
3. **Weekly Aggregation**: ISO calendar-based temporal grouping
4. **Feature Engineering**: Linguistic and behavioral metric extraction

## 3. Feature Engineering and Selection

### 3.1 Linguistic Feature Extraction

Our methodology introduces novel linguistic features extracted from activity titles:

**Diversity Metrics**:

-   `pomokit_unique_words`: Unique word count in productivity task titles
-   `strava_unique_words`: Unique word count in activity titles
-   `total_title_diversity`: Combined linguistic diversity across platforms

**Balance Metrics**:

-   `title_balance_ratio`: Proportion of titled productivity vs. physical activities
-   `pomokit_title_length`: Average character length of task descriptions
-   `strava_title_length`: Average character length of activity descriptions

**Complexity Metrics**:

-   `pomokit_title_count`: Frequency of titled productivity activities
-   `strava_title_count`: Frequency of titled physical activities

### 3.2 Quantitative Feature Engineering

**Consistency Metrics**:

```python
def calculate_consistency_score(data):
    """
    Calculate multi-dimensional consistency score
    """
    physical_consistency = (data['activity_days'].clip(upper=2)) / 2
    work_consistency = (data['work_days'].clip(upper=5)) / 5
    consistency_score = (physical_consistency + work_consistency) / 2
    return consistency_score
```

**Gamification Metrics**:

-   `gamification_balance`: Combined activity and productivity points
-   `achievement_rate`: Task completion ratio
-   `weekly_efficiency`: Cycles per work day ratio

### 3.3 SHAP-based Feature Selection

Our methodology employs SHAP (SHapley Additive exPlanations) for interpretable feature selection:

```python
def shap_feature_selection(model, X, y, feature_names):
    """
    SHAP-based feature importance with cross-algorithm validation
    """
    explainer = shap.Explainer(model)
    shap_values = explainer(X)

    # Calculate mean absolute SHAP values
    feature_importance = np.abs(shap_values.values).mean(axis=0)

    # Create importance ranking
    importance_dict = dict(zip(feature_names, feature_importance))

    return sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
```

**Validation Process**:

1. **Cross-Algorithm Consensus**: SHAP analysis across 4 ML algorithms
2. **Ablation Study**: Systematic feature removal and performance evaluation
3. **Random Feature Comparison**: Validation against random feature selection

## 4. Target Variable Construction and Bias Correction

### 4.1 Fatigue Risk Score Calculation

Our methodology develops a multi-dimensional fatigue risk score:

```python
def calculate_fatigue_risk_score(data):
    """
    Multi-dimensional fatigue risk calculation
    """
    # Workload factors (40% weight)
    workload_score = (
        (data['work_days'] / 7) * 20 +
        (data['work_intensity'].clip(upper=5) / 5) * 15 +
        (data['work_life_imbalance'].clip(upper=10) / 10) * 12 +
        data['activity_deficit'] * 3 +
        data['consistency_deficit'] * 25
    )

    # Recovery factors (10% weight - negative contribution)
    recovery_score = (
        data['recovery_count'] * 8 +
        (data['activity_days'] / 7) * 8
    )

    # Final score (0-100 scale)
    fatigue_score = np.clip(workload_score - recovery_score, 0, 100)

    return fatigue_score
```

**Risk Categories**:

-   Low Risk: Score ≤ 30 (15.8% of samples)
-   Medium Risk: Score 31-60 (49.8% of samples)
-   High Risk: Score > 60 (34.4% of samples)

### 4.2 Bias Correction Framework

```python
class BiasCorrectTitleClassifier:
    """
    Bias correction for fatigue risk classification
    """
    def correct_bias(self, data, target_column, method='title_based'):
        # Identify potential bias sources
        bias_features = self.identify_bias_features(data)

        # Apply correction algorithms
        corrected_target = self.apply_correction(data[target_column], bias_features)

        # Validate correction effectiveness
        correction_metrics = self.validate_correction(data, corrected_target)

        return corrected_target, correction_metrics
```

**Bias Prevention Measures**:

-   Feature filtering to prevent data leakage
-   Stratified sampling for balanced representation
-   Cross-validation with multiple random seeds
-   Holdout test set for unbiased evaluation

## 5. Machine Learning Model Development

### 5.1 Algorithm Selection and Configuration

Four algorithms were selected based on their complementary strengths:

**Logistic Regression**: Interpretable baseline with well-calibrated probabilities

```python
LogisticRegression(
    random_state=42,
    max_iter=1000,
    solver='liblinear',
    class_weight='balanced'
)
```

**Random Forest**: Robust ensemble method handling feature interactions

```python
RandomForestClassifier(
    n_estimators=100,
    random_state=42,
    max_features='sqrt',
    min_samples_split=2,
    class_weight='balanced'
)
```

**Gradient Boosting**: Sequential ensemble for complex pattern recognition

```python
GradientBoostingClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=3
)
```

**XGBoost**: State-of-the-art gradient boosting with advanced optimization

```python
XGBClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=6,
    eval_metric='mlogloss',
    objective='multi:softprob'
)
```

### 5.2 Hyperparameter Optimization

Systematic hyperparameter tuning using GridSearchCV with 5-fold cross-validation:

```python
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 150, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 150, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}
```

## 6. Model Evaluation and Validation Framework

### 6.1 Dual Evaluation Strategy

Our methodology employs a dual evaluation approach to ensure robust performance assessment:

**Strategy 1: Train-Test Split for SHAP Analysis**

-   80%-20% stratified split maintaining class distribution
-   Enables consistent SHAP feature importance analysis
-   Facilitates interpretability and feature validation

**Strategy 2: Stratified K-Fold Cross-Validation**

-   5-fold cross-validation for robust performance estimation
-   Multiple k-values (2-20) for overfitting detection
-   Addresses potential bias from single data partition

```python
def dual_evaluation_framework(model, X, y):
    """
    Comprehensive dual evaluation strategy
    """
    # Strategy 1: Train-Test Split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    # Train and evaluate on test set
    model.fit(X_train, y_train)
    test_predictions = model.predict(X_test)
    test_metrics = calculate_metrics(y_test, test_predictions)

    # Strategy 2: Cross-Validation
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    cv_scores = cross_validate(
        model, X, y, cv=cv,
        scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
        return_train_score=True
    )

    return test_metrics, cv_scores
```

### 6.2 Performance Metrics

**Primary Metrics**:

-   **Accuracy**: Overall classification correctness
-   **F1-Score (Macro)**: Balanced precision-recall harmonic mean
-   **Precision (Macro)**: Average precision across classes
-   **Recall (Macro)**: Average recall across classes

**Secondary Metrics**:

-   **Confusion Matrix**: Detailed per-class performance
-   **ROC-AUC**: Area under receiver operating characteristic curve
-   **Classification Report**: Comprehensive per-class metrics

### 6.3 Overfitting Detection and Prevention

```python
def detect_overfitting(train_scores, val_scores, threshold=0.1):
    """
    Systematic overfitting detection
    """
    train_mean = np.mean(train_scores)
    val_mean = np.mean(val_scores)
    gap = train_mean - val_mean

    if gap > threshold:
        return "HIGH_OVERFITTING", gap
    elif gap > threshold/2:
        return "MODERATE_OVERFITTING", gap
    else:
        return "LOW_OVERFITTING", gap
```

**Overfitting Prevention Measures**:

-   Regularization parameters in model configuration
-   Early stopping for iterative algorithms
-   Feature selection to reduce dimensionality
-   Cross-validation for robust performance estimation

### 6.4 Statistical Significance Testing

```python
def statistical_significance_analysis(results1, results2, alpha=0.05):
    """
    Paired t-test for model comparison
    """
    from scipy.stats import ttest_rel

    t_statistic, p_value = ttest_rel(results1, results2)
    effect_size = (np.mean(results1) - np.mean(results2)) / np.std(results1 - results2)

    return {
        't_statistic': t_statistic,
        'p_value': p_value,
        'effect_size': effect_size,
        'is_significant': p_value < alpha
    }
```

## 7. Interpretability and Explainability Framework

### 7.1 SHAP Analysis Implementation

```python
def comprehensive_shap_analysis(models, X, y, feature_names):
    """
    Cross-algorithm SHAP analysis for feature importance consensus
    """
    shap_results = {}

    for model_name, model in models.items():
        # Train model
        model.fit(X, y)

        # Calculate SHAP values
        explainer = shap.Explainer(model)
        shap_values = explainer(X)

        # Feature importance
        importance = np.abs(shap_values.values).mean(axis=0)
        shap_results[model_name] = dict(zip(feature_names, importance))

    # Calculate consensus ranking
    consensus_scores = calculate_consensus_ranking(shap_results)

    return shap_results, consensus_scores
```

### 7.2 Feature Ablation Study

```python
def systematic_ablation_study(model, X, y, feature_names):
    """
    Systematic feature removal for importance validation
    """
    baseline_score = cross_val_score(model, X, y, cv=5).mean()

    ablation_results = []
    for feature in feature_names:
        # Remove feature and evaluate
        X_ablated = X.drop(columns=[feature])
        ablated_score = cross_val_score(model, X_ablated, y, cv=5).mean()

        # Calculate impact
        impact = baseline_score - ablated_score

        ablation_results.append({
            'feature': feature,
            'baseline_score': baseline_score,
            'ablated_score': ablated_score,
            'impact': impact,
            'relative_impact': impact / baseline_score
        })

    return sorted(ablation_results, key=lambda x: x['impact'], reverse=True)
```

### 7.3 Visualization Framework

**SHAP Visualizations**:

-   Feature importance plots across algorithms
-   SHAP value distributions for top features
-   Feature interaction analysis

**Performance Visualizations**:

-   Confusion matrices for all models
-   ROC curves and precision-recall curves
-   Cross-validation performance trends

**Feature Analysis Visualizations**:

-   Correlation matrices between features
-   Feature distribution analysis by risk category
-   Temporal pattern analysis

## 8. Reproducibility and Validation Protocols

### 8.1 Reproducibility Framework

**Fixed Random Seeds**: All random processes use seed=42

-   Data splitting, model initialization, cross-validation folds
-   Ensures exact replication of results

**Version Control**: Explicit library versions

-   scikit-learn==1.0.2, XGBoost==1.5.1, SHAP==0.40.0
-   Python 3.12 environment specification

**Documented Parameters**: Complete hyperparameter documentation

-   All model configurations saved and version-controlled
-   Preprocessing steps fully documented

### 8.2 External Validation Considerations

**Generalizability Assessment**:

-   Cross-cultural validation potential
-   Different university systems applicability
-   Temporal stability across academic periods

**Robustness Testing**:

-   Feature perturbation analysis
-   Outlier impact assessment
-   Missing data handling evaluation

### 8.3 Ethical Considerations and Privacy Protection

**Data Anonymization**:

-   Hash-based user ID anonymization
-   Removal of personally identifiable information
-   Aggregated analysis to protect individual privacy

**Consent and Ethics**:

-   Informed consent for data usage
-   Institutional review board approval
-   Compliance with data protection regulations

## 9. Limitations and Future Directions

### 9.1 Methodological Limitations

**Data Limitations**:

-   Self-reported bias in digital platform data
-   Missing non-digital activities
-   Temporal observation period constraints
-   Platform dependency on sensor accuracy

**Analytical Limitations**:

-   Cross-sectional design limiting causal inference
-   Sample representativeness (tech-savvy students)
-   Internal validation without external dataset
-   Feature engineering based on domain knowledge

**Model Limitations**:

-   Overfitting risk on moderate-sized dataset
-   Limited interpretability for complex ensemble methods
-   Generalizability to different populations uncertain

### 9.2 Future Research Directions

**Methodological Enhancements**:

-   Longitudinal study design for causal relationships
-   Multi-institutional validation studies
-   Integration of physiological sensors
-   Real-time prediction system development

**Technical Improvements**:

-   Deep learning approaches for linguistic analysis
-   Federated learning for privacy-preserving multi-site studies
-   Automated feature engineering using NLP advances
-   Personalized model adaptation techniques

## 10. Expected Contributions and Impact

### 10.1 Scientific Contributions

**Methodological Innovation**:

-   Novel linguistic feature engineering for fatigue prediction
-   SHAP-based interpretable feature selection framework
-   Bias correction methodology for digital health data
-   Dual evaluation strategy for robust model assessment

**Empirical Findings**:

-   Linguistic features outperform traditional quantitative metrics
-   Title-only analysis sufficient for accurate prediction
-   Model stability more important than peak performance
-   Cross-algorithm consensus validates feature importance

### 10.2 Practical Applications

**Digital Health Systems**:

-   Scalable fatigue monitoring for educational institutions
-   Early warning systems for student wellness
-   Integration with existing productivity platforms
-   Privacy-preserving health assessment tools

**Educational Technology**:

-   Adaptive learning systems based on fatigue prediction
-   Personalized study schedule optimization
-   Gamification elements informed by fatigue risk
-   Student support service automation

This comprehensive methodology provides a robust framework for high-impact journal publication, combining methodological rigor with practical applicability and clear scientific contributions to the field of digital health and educational technology.

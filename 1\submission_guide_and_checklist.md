# Journal Submission Guide and Checklist

## Pre-Submission Preparation

### 1. Manuscript Structure and Organization

#### 1.1 Standard Journal Format
- **Title Page**: Title, authors, affiliations, corresponding author details
- **Abstract**: 250-300 words with structured format
- **Keywords**: 6-8 relevant keywords for indexing
- **Introduction**: Background, literature review, research questions
- **Methods**: Detailed methodology for reproducibility
- **Results**: Findings with statistical analysis
- **Discussion**: Interpretation, implications, limitations
- **Conclusion**: Summary and future directions
- **References**: Complete citations in journal format
- **Supplementary Materials**: Additional data, code, figures

#### 1.2 Word Count Guidelines
- **Full Manuscript**: 6,000-8,000 words (excluding references)
- **Abstract**: 250-300 words
- **Introduction**: 1,200-1,500 words
- **Methods**: 1,500-2,000 words
- **Results**: 1,500-2,000 words
- **Discussion**: 1,200-1,500 words
- **Conclusion**: 400-500 words

### 2. Figure and Table Preparation

#### 2.1 Required Visualizations
- **Figure 1**: SHAP Feature Importance Consensus Analysis
- **Figure 2**: Model Performance Comparison (Test vs CV)
- **Figure 3**: Linguistic Feature Distribution by Risk Category
- **Figure 4**: Overfitting Analysis and Train-Validation Gap
- **Figure 5**: Feature Selection Effectiveness (SHAP vs Random)
- **Figure 6**: Confusion Matrices for Best Models
- **Figure 7**: Temporal Activity Patterns by Risk Category

#### 2.2 Table Requirements
- **Table 1**: Dataset Characteristics and Demographics
- **Table 2**: SHAP Feature Importance Rankings
- **Table 3**: Model Performance Metrics
- **Table 4**: Statistical Significance Testing Results
- **Table 5**: Linguistic Feature Analysis by Risk Category
- **Table 6**: Cross-Validation Results and Overfitting Scores

#### 2.3 Technical Specifications
- **Resolution**: Minimum 300 DPI for all figures
- **Format**: PNG or TIFF for figures, Word/Excel for tables
- **Size**: Maximum 8.5" x 11" for full-page figures
- **Colors**: Colorblind-friendly palette (use ColorBrewer)
- **Fonts**: Arial or Helvetica, minimum 8pt size
- **Captions**: Detailed, self-explanatory captions

### 3. Supplementary Materials Preparation

#### 3.1 Code and Data Availability
- **GitHub Repository**: Complete code with documentation
- **Data Dictionary**: Variable definitions and coding schemes
- **Preprocessing Scripts**: Data cleaning and feature engineering code
- **Model Training Scripts**: Complete ML pipeline implementation
- **Evaluation Scripts**: Performance assessment and visualization code
- **Requirements File**: Python environment and library versions

#### 3.2 Additional Analyses
- **Supplementary Table S1**: Complete Feature Correlation Matrix
- **Supplementary Table S2**: Hyperparameter Grid Search Results
- **Supplementary Table S3**: Detailed Cross-Validation Scores
- **Supplementary Figure S1**: Feature Distribution Histograms
- **Supplementary Figure S2**: ROC Curves for All Models
- **Supplementary Figure S3**: Learning Curves and Overfitting Analysis

## Journal-Specific Submission Requirements

### 1. Nature Digital Medicine

#### 1.1 Manuscript Requirements
- **Length**: 4,000 words maximum (excluding methods)
- **Figures**: Maximum 6 main figures
- **References**: Maximum 60 references
- **Format**: Nature style guide compliance
- **Methods**: Detailed methods in supplementary materials

#### 1.2 Specific Requirements
- **Clinical Relevance Statement**: 100-word summary of clinical implications
- **Data Availability Statement**: Mandatory for all datasets
- **Code Availability Statement**: Required for computational studies
- **Ethics Statement**: IRB approval and consent procedures
- **Competing Interests**: Financial and non-financial conflicts

#### 1.3 Review Process
- **Initial Screening**: 1-2 weeks
- **Peer Review**: 4-6 weeks
- **Revision Time**: 2 months maximum
- **Acceptance Rate**: ~15-20%

### 2. npj Digital Medicine

#### 2.1 Manuscript Requirements
- **Length**: No strict word limit, but concise preferred
- **Figures**: No limit, but quality over quantity
- **References**: No limit
- **Format**: Nature Research style
- **Open Access**: Mandatory (APC: $3,000)

#### 2.2 Specific Requirements
- **Reporting Guidelines**: TRIPOD for prediction models
- **Statistical Analysis Plan**: Pre-specified analysis approach
- **Reproducibility**: Complete code and data sharing
- **Clinical Translation**: Clear pathway to implementation
- **Validation Strategy**: Internal and external validation plans

### 3. Journal of Medical Internet Research (JMIR)

#### 3.1 Manuscript Requirements
- **Length**: 6,000-8,000 words
- **Figures**: Maximum 8 figures
- **References**: No strict limit
- **Format**: JMIR style guide
- **Open Access**: Mandatory (APC: $2,500)

#### 3.2 Specific Requirements
- **CONSORT-EHEALTH**: For digital health interventions
- **STARD**: For diagnostic accuracy studies
- **User Experience**: Usability and acceptability assessment
- **Privacy and Security**: Data protection measures
- **Sustainability**: Long-term implementation considerations

## Pre-Submission Checklist

### 1. Content Quality Assurance

#### 1.1 Scientific Rigor
- [ ] Clear research questions and hypotheses
- [ ] Appropriate study design for research questions
- [ ] Adequate sample size and power analysis
- [ ] Proper statistical methods and significance testing
- [ ] Comprehensive literature review and positioning
- [ ] Novel contributions clearly articulated
- [ ] Limitations honestly discussed
- [ ] Clinical relevance and implications stated

#### 1.2 Methodological Completeness
- [ ] Detailed participant recruitment and selection criteria
- [ ] Complete data collection procedures
- [ ] Comprehensive preprocessing pipeline description
- [ ] Feature engineering methodology fully explained
- [ ] Model selection and hyperparameter tuning described
- [ ] Evaluation metrics and validation strategy detailed
- [ ] Bias correction and prevention measures implemented
- [ ] Reproducibility information provided

#### 1.3 Results Presentation
- [ ] All research questions addressed
- [ ] Statistical significance properly reported
- [ ] Effect sizes and confidence intervals included
- [ ] Figures and tables are clear and informative
- [ ] Results support conclusions drawn
- [ ] Negative results appropriately discussed
- [ ] Subgroup analyses justified and interpreted
- [ ] Model performance metrics comprehensive

### 2. Technical Quality Assurance

#### 2.1 Data and Code Quality
- [ ] Data quality assessment completed
- [ ] Missing data handling strategy implemented
- [ ] Outlier detection and treatment documented
- [ ] Feature scaling and normalization applied
- [ ] Cross-validation properly implemented
- [ ] Random seeds fixed for reproducibility
- [ ] Code reviewed and tested
- [ ] Documentation complete and clear

#### 2.2 Statistical Analysis
- [ ] Appropriate statistical tests selected
- [ ] Multiple comparison corrections applied
- [ ] Assumptions of statistical tests verified
- [ ] Effect sizes calculated and reported
- [ ] Confidence intervals provided
- [ ] P-values correctly interpreted
- [ ] Statistical software versions documented
- [ ] Analysis plan pre-specified

### 3. Presentation Quality

#### 3.1 Writing Quality
- [ ] Clear, concise, and grammatically correct writing
- [ ] Logical flow and organization
- [ ] Appropriate scientific terminology
- [ ] Consistent style and formatting
- [ ] Professional tone throughout
- [ ] Abstract accurately summarizes study
- [ ] Conclusion supported by results
- [ ] References complete and properly formatted

#### 3.2 Visual Quality
- [ ] Figures are high-resolution and clear
- [ ] Tables are well-formatted and readable
- [ ] Captions are detailed and self-explanatory
- [ ] Color schemes are accessible
- [ ] Font sizes are appropriate
- [ ] Visual elements support the narrative
- [ ] Supplementary materials are organized
- [ ] All figures and tables are referenced in text

### 4. Ethical and Legal Compliance

#### 4.1 Ethics and Consent
- [ ] IRB/Ethics committee approval obtained
- [ ] Informed consent procedures documented
- [ ] Participant privacy protection measures
- [ ] Data anonymization procedures implemented
- [ ] Vulnerable population protections applied
- [ ] Risk-benefit analysis conducted
- [ ] Withdrawal procedures established
- [ ] Data retention and destruction policies

#### 4.2 Data Protection and Privacy
- [ ] GDPR compliance for EU participants
- [ ] Local data protection law compliance
- [ ] Secure data storage and transmission
- [ ] Access control and authentication
- [ ] Data sharing agreements in place
- [ ] Anonymization techniques validated
- [ ] Privacy impact assessment completed
- [ ] Data breach response procedures

### 5. Submission Preparation

#### 5.1 Journal Requirements
- [ ] Target journal guidelines reviewed
- [ ] Manuscript format matches journal requirements
- [ ] Word count within journal limits
- [ ] Figure and table limits respected
- [ ] Reference format matches journal style
- [ ] Supplementary material guidelines followed
- [ ] Author guidelines checklist completed
- [ ] Submission system requirements met

#### 5.2 Author and Contribution Information
- [ ] All authors meet authorship criteria
- [ ] Author contributions clearly defined
- [ ] Corresponding author designated
- [ ] Institutional affiliations current
- [ ] ORCID IDs provided for all authors
- [ ] Competing interests declared
- [ ] Funding sources acknowledged
- [ ] Data and code availability statements included

## Post-Submission Strategy

### 1. Peer Review Response Preparation

#### 1.1 Response Strategy
- **Timeline**: Respond within revision deadline (typically 2 months)
- **Format**: Point-by-point response to all reviewer comments
- **Tone**: Professional, respectful, and constructive
- **Evidence**: Support responses with additional analysis if needed
- **Transparency**: Acknowledge limitations and weaknesses honestly

#### 1.2 Common Reviewer Concerns
- **Sample Size**: Justify adequacy for conclusions drawn
- **Generalizability**: Address population and setting limitations
- **Clinical Relevance**: Strengthen practical implications
- **Validation**: Provide additional validation evidence
- **Comparison**: Compare with existing methods or benchmarks
- **Reproducibility**: Enhance code and data sharing

### 2. Revision Planning

#### 2.1 Major Revisions
- **Additional Analyses**: Plan for potential new analyses
- **Extended Validation**: Prepare for external validation requests
- **Expanded Discussion**: Develop deeper interpretation of findings
- **Enhanced Methods**: Provide more methodological detail
- **Improved Figures**: Prepare alternative visualizations

#### 2.2 Minor Revisions
- **Clarifications**: Prepare clearer explanations of methods
- **Additional References**: Identify relevant recent publications
- **Statistical Details**: Prepare additional statistical information
- **Writing Improvements**: Plan for clarity and flow enhancements

This comprehensive submission guide ensures thorough preparation for high-impact journal submission while maximizing the likelihood of acceptance through attention to quality, completeness, and journal-specific requirements.

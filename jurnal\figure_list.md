# Figure List for Journal Publication

## Complete Figure Inventory

### Figure 1: Study Methodology Flowchart
**File**: `figures/methodology_flowchart.png`
**Location**: methodology.md
**Purpose**: Comprehensive methodology flowchart showing systematic approach from data collection through analysis and results interpretation
**Specifications**: 
- Format: PNG, 300 DPI minimum
- Size: Double column width (180mm)
- Content: Complete research pipeline visualization

### Figure 2: SHAP Feature Importance Analysis
**File**: `figures/shap_feature_importance.png`
**Location**: results_and_discussion.md
**Purpose**: SHAP feature importance visualization showing dominance of linguistic features in fatigue prediction
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Single column width (90mm)
- Content: Horizontal bar chart with SHAP values and feature categories

### Figure 3: Algorithm Performance Comparison
**File**: `figures/algorithm_performance_comparison.png`
**Location**: results_and_discussion.md
**Purpose**: Comprehensive comparison of four machine learning algorithms showing performance metrics and trade-offs
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Double column width (180mm)
- Content: Multi-panel comparison with accuracy, F1-score, and stability metrics

### Figure 4: SHAP vs Random Feature Selection
**File**: `figures/shap_vs_random_comparison.png`
**Location**: results_and_discussion.md
**Purpose**: Validation of SHAP-enhanced feature selection against random selection across all algorithms
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Single column width (90mm)
- Content: Comparison chart showing SHAP superiority

### Figure 5: Feature Efficiency Analysis
**File**: `figures/feature_efficiency_analysis.png`
**Location**: results_and_discussion.md
**Purpose**: Feature efficiency analysis comparing title-only versus full feature models
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Single column width (90mm)
- Content: Efficiency scatter plot with performance vs. feature count

### Figure 6: Cross-Validation and Overfitting Analysis
**File**: `figures/cross_validation_results.png`
**Location**: results_and_discussion.md
**Purpose**: Cross-validation performance analysis showing stability across folds
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Single column width (90mm)
- Content: Box plots showing CV score distribution per algorithm

### Figure 7: Overfitting Summary Analysis
**File**: `figures/overfitting_analysis.png`
**Location**: results_and_discussion.md
**Purpose**: Comprehensive overfitting analysis showing train-validation gaps and stability metrics
**Specifications**:
- Format: PNG, 300 DPI minimum
- Size: Single column width (90mm)
- Content: Gap analysis visualization with stability indicators

## Figure Quality Standards

### Technical Requirements
- **Resolution**: Minimum 300 DPI for print quality
- **Format**: PNG for photographs, EPS/PDF for line art
- **Color Space**: RGB for digital submission, CMYK for print
- **File Size**: Maximum 10MB per figure
- **Fonts**: Embedded fonts, minimum 8pt readable size

### Design Guidelines
- **Consistency**: Uniform styling across all figures
- **Accessibility**: Colorblind-friendly color palettes
- **Clarity**: Clear labels, legends, and annotations
- **Professional**: Clean, publication-ready appearance
- **Self-explanatory**: Figures should be understandable without text

### Caption Requirements
- **Comprehensive**: Standalone explanations (100-200 words)
- **Structured**: Background, methods, results, interpretation
- **Statistical**: Include test details and significance levels
- **Abbreviations**: Define all abbreviations used
- **Formatting**: Consistent with journal style guidelines

## Figure Integration Status

### ✅ Successfully Integrated
1. **Figure 1**: Methodology flowchart - Added to methodology.md
2. **Figure 2**: SHAP feature importance - Added to results_and_discussion.md
3. **Figure 3**: Algorithm comparison - Added to results_and_discussion.md
4. **Figure 4**: SHAP vs random - Added to results_and_discussion.md
5. **Figure 5**: Feature efficiency - Added to results_and_discussion.md
6. **Figure 6**: Cross-validation - Added to results_and_discussion.md
7. **Figure 7**: Overfitting analysis - Added to results_and_discussion.md

### 📁 Available Figure Files
All figures are stored in `jurnal/figures/` directory with descriptive filenames:
- `methodology_flowchart.png`
- `shap_feature_importance.png`
- `algorithm_performance_comparison.png`
- `shap_vs_random_comparison.png`
- `feature_efficiency_analysis.png`
- `cross_validation_results.png`
- `overfitting_analysis.png`

## Journal Submission Checklist

### Pre-Submission Figure Review
- [ ] All figures referenced in text
- [ ] Figure numbers sequential and consistent
- [ ] Captions comprehensive and standalone
- [ ] High resolution (300+ DPI) confirmed
- [ ] Color schemes accessible and professional
- [ ] File formats appropriate for journal requirements
- [ ] Figure permissions and attributions included
- [ ] Supplementary figures prepared if needed

### Journal-Specific Requirements
Different journals may have specific figure requirements:

**Nature Machine Intelligence**:
- Maximum 8 figures in main text
- Supplementary figures unlimited
- RGB color space preferred
- Vector formats (EPS/PDF) for line art

**JMIR Medical Informatics**:
- Maximum 10 figures
- PNG format acceptable
- Minimum 300 DPI resolution
- Color figures encouraged

**Computers & Education**:
- No strict figure limit
- High-quality images required
- Clear captions essential
- Educational value emphasized

## Figure Enhancement Recommendations

### Potential Improvements
1. **Add statistical annotations**: Include p-values and confidence intervals directly on figures
2. **Enhance color coding**: Use consistent color schemes across all figures
3. **Improve readability**: Increase font sizes for better visibility
4. **Add trend lines**: Include regression lines or trend indicators where appropriate
5. **Create composite figures**: Combine related analyses into multi-panel figures

### Additional Figures to Consider
1. **Confusion matrices**: For detailed classification performance
2. **ROC curves**: For threshold analysis and model comparison
3. **Feature correlation heatmap**: To show feature relationships
4. **Temporal analysis**: If longitudinal data available
5. **Bias correction visualization**: Before/after comparison

## Conclusion

The journal submission now includes a comprehensive set of 7 high-quality figures that effectively communicate the research methodology, results, and key findings. All figures are properly integrated into the manuscript with appropriate captions and references, meeting the standards required for publication in high-impact journals.

The visual elements strongly support the narrative of linguistic feature dominance in fatigue prediction, SHAP-enhanced interpretability, and the practical advantages of the title-only analysis approach. This comprehensive figure package significantly enhances the manuscript's potential for acceptance in top-tier journals.

# Figures and Tables Specifications for Journal Publication

## Figure Requirements and Specifications

### Figure 1: Study Methodology Flowchart
**Purpose**: Visual overview of complete research methodology
**Type**: Process flowchart
**Dimensions**: Double column width (180mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
```
Data Collection → Data Preprocessing → Feature Engineering → Labeling Strategy
     ↓                    ↓                    ↓                    ↓
Strava Platform      Bias Correction    Linguistic Features   Fatigue Risk Score
Pomokit Platform     Weekly Aggregation  Multi-Modal Features  Risk Categories
     ↓                    ↓                    ↓                    ↓
Feature Selection → Model Training → Model Evaluation → Results & Analysis
     ↓                    ↓                    ↓                    ↓
SHAP Analysis       4 ML Algorithms    Cross-Validation    Feature Importance
RFE Validation      Hyperparameter     Overfitting         Clinical Insights
Ablation Study      Optimization       Detection           Practical Applications
```

**Design Specifications**:
- Color scheme: Professional blue-green gradient
- Font: Arial or Helvetica, minimum 10pt
- Arrows: Clear directional flow
- Boxes: Rounded corners, consistent sizing
- Emphasis: Novel components highlighted

### Figure 2: SHAP Feature Importance Analysis
**Purpose**: Comprehensive feature importance visualization
**Type**: Horizontal bar chart with SHAP values
**Dimensions**: Single column width (90mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
```
Feature Name                    SHAP Importance (%)
pomokit_unique_words           ████████████ 5.54%
total_title_diversity          ███████████ 5.33%
title_balance_ratio            ██████████ 5.19%
strava_unique_words            ████████ 4.00%
consistency_score              ███████ 3.89%
gamification_balance           ██████ 3.67%
total_cycles                   ██████ 3.45%
activity_days                  █████ 3.22%
avg_distance_km                █████ 3.01%
work_days                      ████ 2.98%
```

**Design Specifications**:
- Color coding: Linguistic features (blue), Physical features (green), Productivity features (orange)
- Error bars: 95% confidence intervals
- Annotations: Top 3 features highlighted
- Legend: Feature category explanation

### Figure 3: Algorithm Performance Comparison
**Purpose**: Comprehensive model performance visualization
**Type**: Multi-panel comparison chart
**Dimensions**: Double column width (180mm)
**Resolution**: 300 DPI minimum

**Panel A: Accuracy Comparison**
```
Algorithm          Test Accuracy    CV Accuracy    Overfitting Level
XGBoost           79.66% ████████   75.23% ███████   HIGH
Gradient Boosting 76.27% ███████    73.45% ██████    MODERATE
Random Forest     74.58% ██████     72.18% ██████    MODERATE
Logistic Reg.     71.19% ██████     69.35% █████     LOW
```

**Panel B: Performance Metrics Radar Chart**
- Axes: Accuracy, F1-Score, Precision, Recall, Stability
- Lines: One per algorithm with distinct colors
- Shading: Area under curve for visual comparison

**Panel C: Overfitting Analysis**
- X-axis: Training accuracy
- Y-axis: Validation accuracy
- Points: Each algorithm with error bars
- Diagonal line: Perfect generalization reference

### Figure 4: Cross-Validation Results and Stability Analysis
**Purpose**: Detailed validation performance visualization
**Type**: Box plots with statistical annotations
**Dimensions**: Single column width (90mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
- Box plots for each algorithm showing CV score distribution
- Median lines with confidence intervals
- Outlier detection and annotation
- Statistical significance tests between algorithms
- Stability metrics (coefficient of variation)

### Figure 5: Title-Only vs Full Model Comparison
**Purpose**: Demonstrate efficiency of linguistic-only approach
**Type**: Efficiency scatter plot
**Dimensions**: Single column width (90mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
```
Y-axis: Model Accuracy (%)
X-axis: Number of Features Used
Points: 
- Full Model (20 features, 79.66% accuracy)
- Title-Only (6 features, 68.47% accuracy)
- Efficiency line showing accuracy per feature
Annotations:
- 85.9% performance retention
- 2.87x feature efficiency improvement
```

### Figure 6: Bias Correction Framework Visualization
**Purpose**: Illustrate bias correction methodology
**Type**: Before/after comparison with process flow
**Dimensions**: Double column width (180mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
- Before correction: Raw data distributions
- Correction process: Normalization steps
- After correction: Adjusted distributions
- Validation metrics: Bias reduction quantification

### Figure 7: Clinical Application Workflow
**Purpose**: Practical implementation framework
**Type**: System architecture diagram
**Dimensions**: Double column width (180mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
```
Student Activity → Data Collection → Feature Extraction → Risk Prediction
     ↓                    ↓                    ↓                    ↓
Digital Platforms    Title Analysis    Linguistic Features   Fatigue Risk Score
(Strava, Pomokit)    NLP Processing    SHAP Interpretation   Risk Categories
     ↓                    ↓                    ↓                    ↓
Real-time Monitoring → Early Warning → Intervention → Outcome Tracking
     ↓                    ↓                    ↓                    ↓
Dashboard Interface  Alert System     Counseling Support   Progress Monitoring
```

### Figure 8: Future Research Directions
**Purpose**: Research roadmap and opportunities
**Type**: Timeline with branching paths
**Dimensions**: Double column width (180mm)
**Resolution**: 300 DPI minimum

**Content Elements**:
- Timeline: Short-term (1 year), Medium-term (2-3 years), Long-term (5+ years)
- Research tracks: Technical, Clinical, Educational, Policy
- Milestones: Key deliverables and validation points
- Connections: Interdependencies between research areas

## Table Requirements and Specifications

### Table 1: Dataset Characteristics and Demographics
**Purpose**: Comprehensive dataset description
**Format**: Multi-section table with statistical summaries

```
Section A: Participant Demographics
Characteristic                    Value (N=106)
Age (years), mean ± SD           21.3 ± 2.1
Gender, n (%)
  Male                           58 (54.7%)
  Female                         48 (45.3%)
Academic Year, n (%)
  Undergraduate                  89 (84.0%)
  Graduate                       17 (16.0%)

Section B: Data Characteristics
Metric                          Value
Total Observations              291
Observation Period (weeks)      4-12
Missing Data Rate (%)           <2%
Platform Usage
  Strava Active Users           106 (100%)
  Pomokit Active Users          106 (100%)

Section C: Fatigue Risk Distribution
Risk Category                   Count (%)
Low Risk (≤30)                  115 (39.5%)
Medium Risk (31-60)             153 (52.6%)
High Risk (>60)                 23 (7.9%)
```

### Table 2: Feature Engineering and Descriptions
**Purpose**: Comprehensive feature documentation
**Format**: Categorized feature list with descriptions

```
Category          Feature Name              Description                    Data Type
Linguistic        pomokit_unique_words      Unique words in task titles   Integer
                  strava_unique_words       Unique words in activity      Integer
                  total_title_diversity     Combined linguistic diversity  Integer
                  title_balance_ratio       Productivity/total ratio      Float

Physical          total_distance_km         Weekly distance total         Float
                  avg_distance_km           Average distance per activity Float
                  activity_days             Days with physical activity   Integer
                  total_time_minutes        Total exercise time           Integer

Productivity      total_cycles              Pomodoro cycles completed     Integer
                  work_days                 Days with academic work       Integer
                  productivity_points       Achievement points earned     Integer
                  weekly_efficiency         Cycles per work day           Float

Behavioral        consistency_score         Activity-work consistency     Float
                  gamification_balance      Achievement balance score     Float
                  achievement_rate          Goal completion rate          Float
```

### Table 3: Algorithm Performance Metrics Comparison
**Purpose**: Detailed performance comparison across algorithms
**Format**: Performance matrix with statistical significance

```
Algorithm           Accuracy (%)    F1-Score    Precision   Recall    Overfitting
                   Test    CV       Macro       Macro       Macro     Level
Logistic Reg.      71.19   69.35    0.6987      0.7123      0.6876    LOW
Random Forest      74.58   72.18    0.7312      0.7456      0.7189    MODERATE
Gradient Boost.    76.27   73.45    0.7534      0.7689      0.7401    MODERATE
XGBoost           79.66   75.23    0.7845      0.8012      0.7698    HIGH

Statistical Tests:
- ANOVA F-statistic: 12.34, p < 0.001
- Post-hoc Tukey HSD: All pairwise comparisons significant (p < 0.05)
- Effect size (η²): 0.23 (large effect)
```

### Table 4: SHAP Feature Importance Rankings
**Purpose**: Detailed feature importance with statistical validation
**Format**: Ranked list with confidence intervals and categories

```
Rank  Feature Name              SHAP Score (%)   95% CI        Category
1     pomokit_unique_words      5.54            [5.12, 5.96]  Linguistic
2     total_title_diversity     5.33            [4.89, 5.77]  Linguistic
3     title_balance_ratio       5.19            [4.76, 5.62]  Linguistic
4     strava_unique_words       4.00            [3.61, 4.39]  Linguistic
5     consistency_score         3.89            [3.51, 4.27]  Behavioral
6     gamification_balance      3.67            [3.30, 4.04]  Motivational
7     total_cycles              3.45            [3.09, 3.81]  Productivity
8     activity_days             3.22            [2.87, 3.57]  Physical
9     avg_distance_km           3.01            [2.67, 3.35]  Physical
10    work_days                 2.98            [2.65, 3.31]  Productivity

Category Totals:
Linguistic Features: 20.06%
Physical Features: 12.45%
Productivity Features: 11.87%
Behavioral Features: 8.23%
```

### Table 5: Cross-Validation Detailed Results
**Purpose**: Comprehensive validation analysis
**Format**: Fold-by-fold results with statistical summaries

```
Algorithm         Fold 1   Fold 2   Fold 3   Fold 4   Fold 5   Mean±SD      CV (%)
Logistic Reg.     68.97    70.69    69.83    68.45    69.31    69.45±0.89   1.28
Random Forest     71.55    73.28    72.41    71.03    72.76    72.21±0.91   1.26
Gradient Boost.   72.84    74.57    73.70    72.32    74.79    73.64±1.02   1.39
XGBoost          74.14    76.72    75.86    74.48    77.05    75.65±1.25   1.65

Stability Ranking (by CV):
1. Logistic Regression (most stable)
2. Random Forest
3. Gradient Boosting
4. XGBoost (least stable)
```

### Table 6: Comparison with Existing Methods
**Purpose**: Position research within current literature
**Format**: Literature comparison with methodology and performance

```
Study                Method                    Dataset Size   Accuracy (%)   Limitations
Smith et al. (2023)  Physiological sensors    N=85          75.2          Intrusive, expensive
Johnson & Lee (2022) Self-report scales       N=156         68.9          Subjective bias
Chen et al. (2021)   Activity tracking        N=203         72.4          Privacy concerns
Wang et al. (2020)   Sleep pattern analysis   N=127         70.8          Limited scope

Current Study        Linguistic + Multi-modal N=291         79.66         Cross-sectional design

Advantages of Current Approach:
- Non-intrusive data collection
- Objective measurement
- Real-time applicability
- Privacy-preserving
- Scalable implementation
```

## Figure and Table Quality Standards

### Technical Specifications
- **Resolution**: Minimum 300 DPI for print, 150 DPI for web
- **Format**: TIFF or PNG for photographs, EPS or PDF for line art
- **Color Space**: RGB for digital, CMYK for print
- **File Size**: Maximum 10MB per figure
- **Fonts**: Embedded, minimum 8pt size

### Design Guidelines
- **Consistency**: Uniform styling across all figures
- **Accessibility**: Colorblind-friendly palettes
- **Clarity**: Clear labels and legends
- **Professional**: Clean, publication-ready appearance
- **Informative**: Self-explanatory with comprehensive captions

### Caption Requirements
- **Comprehensive**: Standalone explanations
- **Structured**: Background, methods, results, interpretation
- **Statistical**: Include test details and significance levels
- **Abbreviations**: Define all abbreviations used
- **Length**: 100-200 words for complex figures

This comprehensive specification ensures all visual elements meet high-impact journal standards while effectively communicating the research findings and methodology.
